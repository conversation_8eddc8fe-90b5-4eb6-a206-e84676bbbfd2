// slots service to fetch slots from Supabase
import { createClient } from "@supabase/supabase-js";

const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(
  "https://tbwvioruefqbosojmxho.supabase.co",
  supabaseAnonKey
);

export interface Slots {
  id: string; // Changed to string for UUID
  date: string;
  startTime: string;
  endTime: string;
  price: number;
  isAvailable: boolean;
  isSelected: boolean;
}

// Helper function to format time from 24-hour to 12-hour format
const formatTime = (time24: string): string => {
  const [hours, minutes] = time24.split(":");
  const hour = parseInt(hours);
  const ampm = hour >= 12 ? "pm" : "am";
  const hour12 = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour;
  return `${hour12}:${minutes}${ampm}`;
};

// Helper function to format date to match frontend format
const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr);
  return date.toLocaleDateString("en-US", {
    month: "long",
    day: "2-digit",
    year: "numeric",
  });
};

export const slotsService = {
  async getSlots(date: string) {
    // get all slots for a date
    // return sampleSlots.filter((slot) => slot.date === date);
    try {
      const { data, error } = await supabase
        .from("slots")
        .select("*")
        // .eq('date', date)
        .order("start_time");

      if (error) {
        console.log("an error occurred, ", error);
        return;
      }
      return data;
    } catch (error) {
      console.log("an error occurred: ", error);
    }
  },

  getSlot(slotId: number): Promise<Slots | undefined> {
    // get a single slot by id
    return Promise.resolve(sampleSlots.find((slot) => slot.id === slotId));
  },
};
